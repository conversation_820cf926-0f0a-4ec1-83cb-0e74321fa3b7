#!/usr/bin/env python3
"""
Script to fix the corrupted bot state and implement proper validation.
"""

import json
import yaml
import os
from datetime import datetime
from services.binance_client import BinanceClient

def load_config(path="config.yaml"):
    """Load configuration file."""
    with open(path, 'r') as f:
        return yaml.safe_load(f)

def backup_current_state():
    """Backup the current corrupted state for analysis."""
    if os.path.exists('bot_state.json'):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"bot_state_corrupted_backup_{timestamp}.json"
        
        with open('bot_state.json', 'r') as src:
            with open(backup_name, 'w') as dst:
                dst.write(src.read())
        
        print(f"✓ Backed up corrupted state to: {backup_name}")
        return backup_name
    return None

def analyze_corruption():
    """Analyze the corruption in the current state."""
    print("=" * 60)
    print("ANALYZING STATE CORRUPTION")
    print("=" * 60)
    
    try:
        with open('bot_state.json', 'r') as f:
            state = json.load(f)
        
        print(f"\nFound {len(state.get('trade_history', []))} trades in history")
        
        for i, trade in enumerate(state.get('trade_history', [])):
            print(f"\n--- Trade {i+1} ---")
            
            # Extract key values
            avg_price = trade.get('average_price', 0)
            base_qty = trade.get('total_base_quantity', 0)
            quote_spent = trade.get('total_quote_spent', 0)
            entry_price = trade.get('entry_price', 0)
            
            print(f"Entry Price: {entry_price}")
            print(f"Average Price: {avg_price}")
            print(f"Base Quantity: {base_qty}")
            print(f"Quote Spent: {quote_spent}")
            
            # Check for obvious errors
            errors = []
            
            if base_qty > 1.0:  # More than 1 ETH
                errors.append(f"Suspiciously large quantity: {base_qty}")
            
            if avg_price < 100:  # Less than $100 per ETH
                errors.append(f"Suspiciously low price: {avg_price}")
            
            if quote_spent < 10 and base_qty > 0.01:  # Less than $10 but more than 0.01 ETH
                errors.append(f"Quantity too large for amount spent")
            
            # Check calculation consistency
            if base_qty > 0 and quote_spent > 0:
                calculated_avg = quote_spent / base_qty
                if abs(calculated_avg - avg_price) > 0.01:
                    errors.append(f"Average price calculation error: {calculated_avg:.2f} vs {avg_price:.2f}")
            
            if errors:
                print(f"❌ ERRORS DETECTED:")
                for error in errors:
                    print(f"   - {error}")
            else:
                print(f"✓ Trade looks valid")
                
    except Exception as e:
        print(f"Error analyzing corruption: {e}")

def get_real_binance_state():
    """Get the real state from Binance."""
    print(f"\n" + "=" * 60)
    print("GETTING REAL BINANCE STATE")
    print("=" * 60)
    
    try:
        config = load_config()
        client = BinanceClient(config)
        
        # Get current balances
        usdt_balance = client.get_account_balance('USDT')
        eth_balance = client.get_account_balance('ETH')
        current_price = float(client.get_market_price())
        
        print(f"\nReal Binance State:")
        print(f"USDT Balance: {usdt_balance['free']} (free) + {usdt_balance['locked']} (locked)")
        print(f"ETH Balance: {eth_balance['free']} (free) + {eth_balance['locked']} (locked)")
        print(f"Current ETH Price: {current_price:.2f} USDT")
        
        total_eth = float(eth_balance['free']) + float(eth_balance['locked'])
        total_usdt = float(usdt_balance['free']) + float(usdt_balance['locked'])
        eth_value_usdt = total_eth * current_price
        total_value = total_usdt + eth_value_usdt
        
        print(f"\nPortfolio Summary:")
        print(f"ETH Value: {eth_value_usdt:.2f} USDT")
        print(f"Total Value: {total_value:.2f} USDT")
        
        # Get recent trades to understand what really happened
        print(f"\nRecent Trades:")
        recent_trades = client.get_recent_trades(10)
        
        if recent_trades:
            for i, trade in enumerate(recent_trades[-5:]):  # Last 5 trades
                print(f"Trade {i+1}: {trade['qty']} ETH at {trade['price']} USDT ({trade['side']})")
        else:
            print("No recent trades found")
            
        return {
            'eth_balance': total_eth,
            'usdt_balance': total_usdt,
            'current_price': current_price,
            'total_value': total_value,
            'recent_trades': recent_trades
        }
        
    except Exception as e:
        print(f"Error getting Binance state: {e}")
        return None

def create_clean_state(binance_state=None):
    """Create a clean bot state."""
    print(f"\n" + "=" * 60)
    print("CREATING CLEAN STATE")
    print("=" * 60)
    
    # Start with clean state
    clean_state = {
        "open_position": None,
        "trade_history": []
    }
    
    # If we have ETH balance, try to create a position from it
    if binance_state and binance_state['eth_balance'] > 0.001:  # More than 0.001 ETH
        print(f"\nFound ETH balance: {binance_state['eth_balance']:.8f}")
        
        response = input("Do you want to create a position from existing ETH balance? (y/N): ")
        if response.lower() == 'y':
            # Try to estimate the cost basis
            recent_trades = binance_state.get('recent_trades', [])
            buy_trades = [t for t in recent_trades if t.get('isBuyer', False)]
            
            if buy_trades:
                # Calculate average from recent buys
                total_qty = sum(float(t['qty']) for t in buy_trades)
                total_cost = sum(float(t['quoteQty']) for t in buy_trades)
                avg_price = total_cost / total_qty if total_qty > 0 else binance_state['current_price']
                
                print(f"Estimated average price from recent trades: {avg_price:.2f}")
            else:
                # Use current price as estimate
                avg_price = binance_state['current_price']
                total_cost = binance_state['eth_balance'] * avg_price
                print(f"Using current price as estimate: {avg_price:.2f}")
            
            # Create position
            config = load_config()
            position = {
                "entry_price": avg_price,
                "average_price": avg_price,
                "total_base_quantity": binance_state['eth_balance'],
                "total_quote_spent": binance_state['eth_balance'] * avg_price,
                "take_profit_percentage": config['take_profit_percentage'],
                "trailing_stop_deviation_percentage": config['trailing_stop_deviation_percentage'],
                "take_profit_price": avg_price * (1 + config['take_profit_percentage'] / 100),
                "dca_orders": [],
                "trailing_stop_activated": False,
                "trailing_stop_peak_price": 0,
                "trailing_stop_price": 0,
                "status": "recovered",
                "last_ai_check_ts": 0,
                "recovery_timestamp": datetime.now().timestamp(),
                "recovery_note": "Position recovered from existing balance"
            }
            
            clean_state["open_position"] = position
            print(f"✓ Created recovered position")
    
    return clean_state

def save_clean_state(clean_state):
    """Save the clean state."""
    with open('bot_state.json', 'w') as f:
        json.dump(clean_state, f, indent=4)
    
    print(f"✓ Saved clean state to bot_state.json")

def main():
    """Main fix function."""
    print("BOT STATE CORRUPTION FIX TOOL")
    print("This tool will analyze and fix the corrupted bot state.")
    
    try:
        # Step 1: Backup current state
        backup_file = backup_current_state()
        
        # Step 2: Analyze corruption
        analyze_corruption()
        
        # Step 3: Get real Binance state
        binance_state = get_real_binance_state()
        
        # Step 4: Ask user what to do
        print(f"\n" + "=" * 60)
        print("RECOVERY OPTIONS")
        print("=" * 60)
        print("1. Create completely clean state (no positions)")
        print("2. Try to recover position from Binance balance")
        print("3. Cancel and keep current state")
        
        choice = input("\nChoose option (1-3): ")
        
        if choice == '1':
            clean_state = create_clean_state()
            save_clean_state(clean_state)
            print(f"\n✅ Created clean state with no positions")
            
        elif choice == '2':
            clean_state = create_clean_state(binance_state)
            save_clean_state(clean_state)
            print(f"\n✅ Created state with recovered position")
            
        elif choice == '3':
            print(f"\n⏸️  Cancelled - keeping current state")
            return
            
        else:
            print(f"\n❌ Invalid choice")
            return
        
        print(f"\n" + "=" * 60)
        print("NEXT STEPS")
        print("=" * 60)
        print("1. Test the bot with the new clean state")
        print("2. Monitor logs carefully for any calculation errors")
        print("3. The backup file is available if you need to revert")
        if backup_file:
            print(f"4. Backup saved as: {backup_file}")
        
    except Exception as e:
        print(f"Fix failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
