import logging
import time
from binance import Client, Threaded<PERSON>eb<PERSON><PERSON><PERSON><PERSON><PERSON>, ThreadedDepthCacheManager
from binance.enums import *
from binance.exceptions import BinanceAPIException
from pprint import pprint
import yaml
from dotenv import load_dotenv
import os
import math
import pandas as pd
import pandas_ta_classic as ta
from logger_setup import setup_logger

load_dotenv()

logger = logging.getLogger()

class BinanceClient:
    """
    Handles all communication with the Binance API.
    """
    def __init__(self, config):
        """Initializing the Binance Client."""
        self.config = config
        self.api_key = os.getenv('BINANCE_API_KEY_TESTNET')
        self.api_secret = os.getenv('BINANCE_SECRET_KEY_TESTNET')
        self.trading_pair = config['trading_pair']
        self.client = Client(self.api_key, self.api_secret, testnet=True)
        self.logger = setup_logger(self.config['log_file'])

        self.logger.info("Initialized Binance Client.")

    def get_market_price(self):
        """Fetching the current market price."""
        depth = self.client.get_order_book(symbol=self.trading_pair)
        depth = depth['asks'][0][0]
        self.logger.debug(f"Fetching price for {self.trading_pair}")
        return depth
    
    def get_rsi(self):
        """Fetching historical klines."""
        klines = self.client.get_historical_klines(self.trading_pair, Client.KLINE_INTERVAL_15MINUTE, "1 day ago UTC")
        df = pd.DataFrame(klines, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'close_time', 'quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'])
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        # Calculate RSI
        df.ta.rsi(length=14, append=True)
        # Get last RSI value
        rsi = df['RSI_14'].iloc[-1]
        self.logger.debug(f"Fetching RSI for {self.trading_pair}")
        return rsi

    def get_account_balance(self, asset='USDT'):
        """Fetching the account balance for a given asset."""
        balance = self.client.get_asset_balance(asset=asset)
        self.logger.debug(f"Fetching balance for {asset}")
        return balance

    def execute_market_order(self, side, quantity):
        """executing a market order (BUY or SELL)."""
        try:
            # Adjust quantity to meet exchange requirements
            adjusted_quantity = self._adjust_quantity(quantity)

            # Check balance for sell orders
            if side == SIDE_SELL:
                base_asset = self.trading_pair.replace('USDT', '')  # Extract base asset (e.g., ETH from ETHUSDT)
                balance = self.get_account_balance(base_asset)
                available_balance = float(balance['free']) if balance else 0
                if available_balance < adjusted_quantity:
                    self.logger.error(f"Insufficient {base_asset} balance. Available: {available_balance:.8f}, Required: {adjusted_quantity:.8f}")
                    return None

            self.logger.info(f"Executing {side} order: {adjusted_quantity} {self.trading_pair}")

            order = self.client.create_order(
                symbol=self.trading_pair,
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=adjusted_quantity
                )

            self.logger.info(f"Order executed successfully. Order ID: {order.get('orderId', 'N/A')}, Status: {order.get('status', 'N/A')}")
            return order

        except BinanceAPIException as e:
            self.logger.error(f"Failed to execute {side} order for {quantity} {self.trading_pair}: {e}")
            return None
        except ValueError as e:
            self.logger.error(f"Invalid quantity for {side} order: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error executing {side} order: {e}")
            return None

    def execute_market_order_quote(self, side, quote_amount):
        """Execute a market order using quote currency amount (e.g., USDT amount)."""
        try:
            # Validate minimum notional value
            if not self._validate_min_notional(quote_amount):
                return None

            # Check balance before executing buy orders
            if side == SIDE_BUY:
                balance = self.get_account_balance('USDT')
                available_balance = float(balance['free']) if balance else 0
                if available_balance < quote_amount:
                    self.logger.error(f"Insufficient USDT balance. Available: {available_balance:.2f}, Required: {quote_amount:.2f}")
                    return None

            self.logger.info(f"Executing {side} order with {quote_amount} USDT")

            order = self.client.create_order(
                symbol=self.trading_pair,
                side=side,
                type=ORDER_TYPE_MARKET,
                quoteOrderQty=quote_amount
            )

            self.logger.info(f"Quote order executed successfully. Order ID: {order.get('orderId', 'N/A')}, Status: {order.get('status', 'N/A')}")
            return order

        except BinanceAPIException as e:
            self.logger.error(f"Failed to execute {side} quote order for {quote_amount} USDT: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error executing {side} quote order: {e}")
            return None

    def _validate_min_notional(self, quote_amount):
        """Validate that the order meets minimum notional requirements."""
        try:
            info = self.client.get_symbol_info(self.trading_pair)
            min_notional_filter = next((f for f in info['filters'] if f['filterType'] == 'MIN_NOTIONAL'), None)

            if min_notional_filter:
                min_notional = float(min_notional_filter['minNotional'])
                if quote_amount < min_notional:
                    self.logger.error(f"Order amount {quote_amount} USDT is below minimum notional {min_notional} USDT")
                    return False

            return True
        except Exception as e:
            self.logger.error(f"Error validating minimum notional: {e}")
            return False
    
    def _execute_market_order_test(self, side, quantity):
        """executing a market order (BUY or SELL)."""
        try:
            order = self.client.create_test_order(
                symbol=self.trading_pair,
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=self._adjust_quantity(quantity)           
                )
            return order
        except BinanceAPIException  as e:
            self.logger.error(f"Failed to execute order: {e}")
            return None

    
    def _adjust_quantity(self, quantity):
        """Adjusts quantity to meet exchange requirements for lot size and precision."""
        try:
            info = self.client.get_symbol_info(self.trading_pair)
            lot_size = next(f for f in info['filters'] if f['filterType'] == 'LOT_SIZE')
            step_size = float(lot_size['stepSize'])
            min_qty = float(lot_size['minQty'])
            max_qty = float(lot_size['maxQty'])

            self.logger.debug(f"Symbol {self.trading_pair} - Step size: {step_size}, Min qty: {min_qty}, Max qty: {max_qty}")

            # Check if quantity is below minimum
            if quantity < min_qty:
                raise ValueError(f"Quantity {quantity} is below minimum {min_qty} for {self.trading_pair}")

            # Check if quantity is above maximum
            if quantity > max_qty:
                raise ValueError(f"Quantity {quantity} exceeds maximum {max_qty} for {self.trading_pair}")

            # Adjust to allowed precision
            precision = int(round(-math.log(step_size, 10), 0))
            adjusted_quantity = math.floor(quantity * (10 ** precision)) / (10 ** precision)

            # Final check after adjustment
            if adjusted_quantity < min_qty:
                raise ValueError(f"Adjusted quantity {adjusted_quantity} is still below minimum {min_qty}")

            self.logger.debug(f"Original quantity: {quantity}, Adjusted quantity: {adjusted_quantity}")
            return adjusted_quantity

        except Exception as e:
            self.logger.error(f"Error adjusting quantity: {e}")
            raise


# def _load_config(path):
#         """Loads the YAML configuration file."""
#         try:
#             with open(path, 'r') as f:
#                 file_content = f.read()
#                 #yaml_content = file_content.split('"""')[1]
#                 return yaml.safe_load(file_content)
            
#         except Exception as e:
#             raise Exception(f"Failed to load config from {path}: {e}")



# config = _load_config('config.yaml')

# binace = BinanceClient(config)
# print("Market price: ")
# pprint(binace.get_market_price())
# print("Account balance: ")
# pprint(binace.get_account_balance())
# print("Executing order: ")
# quantity = round(6 / float(binace.get_market_price()), 6)
# print(f"Quantity: {quantity}")
# pprint(binace.execute_market_order_test(SIDE_BUY, quantity))