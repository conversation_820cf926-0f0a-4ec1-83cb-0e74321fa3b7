import logging
import time
from binance import Client, Threaded<PERSON>eb<PERSON><PERSON><PERSON><PERSON><PERSON>, ThreadedDepthCacheManager
from binance.enums import *
from binance.exceptions import BinanceAPIException
from pprint import pprint
import yaml
from dotenv import load_dotenv
import os
import math
import pandas as pd
import pandas_ta_classic as ta
from logger_setup import setup_logger

load_dotenv()

logger = logging.getLogger()

class BinanceClient:
    """
    Handles all communication with the Binance API.
    """
    def __init__(self, config):
        """Initializing the Binance Client."""
        self.config = config
        self.api_key = os.getenv('BINANCE_API_KEY_TESTNET')
        self.api_secret = os.getenv('BINANCE_SECRET_KEY_TESTNET')
        self.trading_pair = config['trading_pair']
        self.client = Client(self.api_key, self.api_secret, testnet=True)
        self.logger = setup_logger(self.config['log_file'])

        self.logger.info("Initialized Binance Client.")

    def get_market_price(self):
        """Fetching the current market price."""
        depth = self.client.get_order_book(symbol=self.trading_pair)
        depth = depth['asks'][0][0]
        self.logger.debug(f"Fetching price for {self.trading_pair}")
        return depth
    
    def get_rsi(self):
        """Fetching historical klines."""
        klines = self.client.get_historical_klines(self.trading_pair, Client.KLINE_INTERVAL_15MINUTE, "1 day ago UTC")
        df = pd.DataFrame(klines, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume', 'close_time', 'quote_asset_volume', 'number_of_trades', 'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'])
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        # Calculate RSI
        df.ta.rsi(length=14, append=True)
        # Get last RSI value
        rsi = df['RSI_14'].iloc[-1]
        self.logger.debug(f"Fetching RSI for {self.trading_pair}")
        return rsi

    def get_account_balance(self, asset='USDT'):
        """Fetching the account balance for a given asset."""
        balance = self.client.get_asset_balance(asset=asset)
        self.logger.debug(f"Fetching balance for {asset}")
        return balance

    def execute_market_order(self, side, quantity):
        """executing a market order (BUY or SELL)."""
        try:
            order = self.client.create_order(
                symbol=self.trading_pair,
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=quantity           
                )
            return order
        except BinanceAPIException  as e:
            self.logger.error(f"Failed to execute order: {e}")
            return None
    
    def _execute_market_order_test(self, side, quantity):
        """executing a market order (BUY or SELL)."""
        try:
            order = self.client.create_test_order(
                symbol=self.trading_pair,
                side=side,
                type=ORDER_TYPE_MARKET,
                quantity=self._adjust_quantity(quantity)           
                )
            return order
        except BinanceAPIException  as e:
            self.logger.error(f"Failed to execute order: {e}")
            return None

    
    def _adjust_quantity(self, quantity):
        info = self.client.get_symbol_info(self.trading_pair)
        lot_size = next(f for f in info['filters'] if f['filterType'] == 'LOT_SIZE')
        step_size = float(lot_size['stepSize'])
        min_qty = float(lot_size['minQty'])

        print(f"Step size: {step_size}, Min qty: {min_qty}")

        # Si es menor que la mínima, no se puede
        if quantity < min_qty:
            raise ValueError(f"Cantidad {quantity} menor que el mínimo {min_qty}")

        # Ajustar al múltiplo permitido
        precision = int(round(-math.log(step_size, 10), 0))
        quantity = math.floor(quantity * (10 ** precision)) / (10 ** precision)
        print(f"Quantity: {quantity}")
        return quantity


# def _load_config(path):
#         """Loads the YAML configuration file."""
#         try:
#             with open(path, 'r') as f:
#                 file_content = f.read()
#                 #yaml_content = file_content.split('"""')[1]
#                 return yaml.safe_load(file_content)
            
#         except Exception as e:
#             raise Exception(f"Failed to load config from {path}: {e}")



# config = _load_config('config.yaml')

# binace = BinanceClient(config)
# print("Market price: ")
# pprint(binace.get_market_price())
# print("Account balance: ")
# pprint(binace.get_account_balance())
# print("Executing order: ")
# quantity = round(6 / float(binace.get_market_price()), 6)
# print(f"Quantity: {quantity}")
# pprint(binace.execute_market_order_test(SIDE_BUY, quantity))