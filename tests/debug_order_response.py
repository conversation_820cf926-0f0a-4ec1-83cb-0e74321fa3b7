#!/usr/bin/env python3
"""
Debug script to understand what Binance is actually returning in order responses.
This will help us fix the calculation errors.
"""

import yaml
import json
from services.binance_client import BinanceClient
from utils.logger_setup import setup_logger

def load_config(path="config.yaml"):
    """Load configuration file."""
    with open(path, 'r') as f:
        return yaml.safe_load(f)

def debug_order_response():
    """Debug what Binance returns in order responses."""
    print("=" * 60)
    print("DEBUGGING BINANCE ORDER RESPONSES")
    print("=" * 60)
    
    config = load_config()
    client = BinanceClient(config)
    logger = setup_logger("debug.log")
    
    # Get current market info
    print(f"\n1. Current Market Information:")
    try:
        current_price = float(client.get_market_price())
        print(f"   Current {config['trading_pair']} price: {current_price:.2f} USDT")
        
        # Calculate expected quantity for $6 order
        expected_qty = 6.0 / current_price
        print(f"   Expected quantity for $6: {expected_qty:.8f} ETH")
        
    except Exception as e:
        print(f"   Error getting market price: {e}")
        return
    
    # Get account balances
    print(f"\n2. Account Balances:")
    try:
        usdt_balance = client.get_account_balance('USDT')
        eth_balance = client.get_account_balance('ETH')
        
        print(f"   USDT - Free: {usdt_balance['free']}, Locked: {usdt_balance['locked']}")
        print(f"   ETH - Free: {eth_balance['free']}, Locked: {eth_balance['locked']}")
        
    except Exception as e:
        print(f"   Error getting balances: {e}")
    
    # Test a small order to see the response format
    print(f"\n3. Testing Order Response Format:")
    print("   WARNING: This will place a REAL test order on testnet!")
    
    response = input("   Continue with test order? (y/N): ")
    if response.lower() != 'y':
        print("   Skipping test order.")
        return
    
    try:
        # Place a small test order
        test_amount = 5.0  # $5 USDT
        print(f"   Placing test order for {test_amount} USDT...")
        
        order_result = client.execute_market_order_quote('BUY', test_amount)
        
        if order_result:
            print(f"\n   ✓ Order executed successfully!")
            print(f"   Full order response:")
            print(json.dumps(order_result, indent=2))
            
            # Extract and analyze key fields
            print(f"\n   Key Fields Analysis:")
            print(f"   - Order ID: {order_result.get('orderId')}")
            print(f"   - Status: {order_result.get('status')}")
            print(f"   - Symbol: {order_result.get('symbol')}")
            print(f"   - Side: {order_result.get('side')}")
            print(f"   - Type: {order_result.get('type')}")
            print(f"   - Original Qty: {order_result.get('origQty')}")
            print(f"   - Executed Qty: {order_result.get('executedQty')}")
            print(f"   - Cumulative Quote Qty: {order_result.get('cummulativeQuoteQty')}")
            print(f"   - Price: {order_result.get('price', 'N/A')}")
            
            # Calculate what we think the values should be
            executed_qty = float(order_result.get('executedQty', 0))
            cumulative_quote_qty = float(order_result.get('cummulativeQuoteQty', 0))
            
            if executed_qty > 0 and cumulative_quote_qty > 0:
                calculated_avg_price = cumulative_quote_qty / executed_qty
                print(f"\n   Calculated Values:")
                print(f"   - Average Price: {calculated_avg_price:.2f} USDT")
                print(f"   - Expected Price: ~{current_price:.2f} USDT")
                print(f"   - Price Difference: {abs(calculated_avg_price - current_price):.2f} USDT")
                print(f"   - Expected Qty: {test_amount / current_price:.8f} ETH")
                print(f"   - Actual Qty: {executed_qty:.8f} ETH")
                print(f"   - Qty Difference: {abs(executed_qty - (test_amount / current_price)):.8f} ETH")
                
                # Sanity checks
                print(f"\n   Sanity Checks:")
                if abs(calculated_avg_price - current_price) > current_price * 0.05:
                    print(f"   ⚠️  Price difference > 5%")
                else:
                    print(f"   ✓ Price looks reasonable")
                    
                if abs(executed_qty - (test_amount / current_price)) > (test_amount / current_price) * 0.05:
                    print(f"   ⚠️  Quantity difference > 5%")
                else:
                    print(f"   ✓ Quantity looks reasonable")
                    
                if cumulative_quote_qty > test_amount * 1.05:
                    print(f"   ⚠️  Spent more than expected")
                else:
                    print(f"   ✓ Spent amount looks reasonable")
            
        else:
            print(f"   ✗ Order failed!")
            
    except Exception as e:
        print(f"   Error executing test order: {e}")
        import traceback
        traceback.print_exc()

def analyze_existing_state():
    """Analyze the current bot state to understand the errors."""
    print(f"\n" + "=" * 60)
    print("ANALYZING EXISTING BOT STATE")
    print("=" * 60)
    
    try:
        with open('bot_state.json', 'r') as f:
            state = json.load(f)
        
        print(f"\nCurrent State:")
        print(json.dumps(state, indent=2))
        
        # Analyze the problematic trade
        if state.get('trade_history'):
            for i, trade in enumerate(state['trade_history']):
                print(f"\n--- Trade {i+1} Analysis ---")
                print(f"Entry Price: {trade.get('entry_price', 'N/A')}")
                print(f"Average Price: {trade.get('average_price', 'N/A')}")
                print(f"Total Base Quantity: {trade.get('total_base_quantity', 'N/A')}")
                print(f"Total Quote Spent: {trade.get('total_quote_spent', 'N/A')}")
                
                # Check calculations
                if trade.get('total_base_quantity') and trade.get('total_quote_spent'):
                    calculated_avg = trade['total_quote_spent'] / trade['total_base_quantity']
                    recorded_avg = trade.get('average_price', 0)
                    
                    print(f"Calculated Avg Price: {calculated_avg:.2f}")
                    print(f"Recorded Avg Price: {recorded_avg:.2f}")
                    print(f"Difference: {abs(calculated_avg - recorded_avg):.2f}")
                    
                    if abs(calculated_avg - recorded_avg) > 0.01:
                        print(f"❌ CALCULATION ERROR DETECTED!")
                    else:
                        print(f"✓ Calculation looks correct")
                
                # Check if quantities make sense
                if trade.get('total_base_quantity', 0) > 0.1:  # More than 0.1 ETH
                    print(f"⚠️  WARNING: Large quantity for small order")
                
                if trade.get('average_price', 0) < 100:  # Less than $100
                    print(f"⚠️  WARNING: Very low average price")
                    
    except Exception as e:
        print(f"Error analyzing state: {e}")

def main():
    """Main debug function."""
    print("BINANCE ORDER RESPONSE DEBUG TOOL")
    print("This tool helps diagnose calculation errors in order processing.")
    
    try:
        analyze_existing_state()
        debug_order_response()
        
        print(f"\n" + "=" * 60)
        print("DEBUG SUMMARY")
        print("=" * 60)
        print("Check the output above to identify:")
        print("1. What Binance actually returns in order responses")
        print("2. Whether our calculations are correct")
        print("3. If there are any data type or field name issues")
        print("4. Whether the test order behaves as expected")
        
    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
