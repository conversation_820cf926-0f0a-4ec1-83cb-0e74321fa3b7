import logging
from typing import Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field
from langchain_openai import ChatOpenAI
from utils.google_news import GoogleNews
import utils.fear_and_greed_utils as fg

logger = logging.getLogger()

# Define the desired JSON structure for the AI's response for a NEW entry
class EntryAnalysis(BaseModel):
    market_sentiment: str = Field(description="Options: 'Very Bullish', 'Bullish', 'Neutral', 'Bearish', 'Very Bearish'")
    volatility_outlook: str = Field(description="Options: 'High', 'Medium', 'Low'")
    confidence_score: float = Field(description="A float between 0.0 and 1.0 for entering a new trade.")

# Define the desired JSON structure for the AI's response for an OPEN position
class PositionAnalysis(BaseModel):
    action: str = Field(description="Recommended action. Options: 'hold', 'sell_now'")
    reasoning: str = Field(description="A brief explanation for the recommended action.")
    new_take_profit_percentage: Optional[float] = Field(None, description="Suggest a new take-profit percentage based on market conditions.")
    new_trailing_stop_deviation_percentage: Optional[float] = Field(None, description="Suggest a new trailing stop deviation based on volatility.")

class AIEngine:
    def __init__(self, config, google_news: GoogleNews):
        self.config = config
        self.llm = ChatOpenAI(temperature=0, model="gpt-5-mini-2025-08-07") # Real implementation
        self.entry_parser = JsonOutputParser(pydantic_object=EntryAnalysis)
        self.position_parser = JsonOutputParser(pydantic_object=PositionAnalysis)
        self.entry_prompt = self._create_entry_prompt_template()
        self.position_prompt = self._create_position_prompt_template()
        self.news_fetcher = google_news

    def _create_entry_prompt_template(self):
        prompt_str = """
        You are an expert crypto market analyst for a trading bot. Your role is to provide a structured assessment of the market conditions for {trading_pair} to decide if it's a good time to enter a NEW trade. Do not give trading advice, only analyze the data.

        **Format Instructions:**
        {format_instructions}

        **Market Data:**
        - Recent News: {news_headlines}
        - Social Media Sentiment Score: {sentiment_score}
        - Technical Indicator (RSI 4h): {rsi}

        Provide your analysis based *only* on the data above.
        """
        return ChatPromptTemplate.from_template(
            template=prompt_str,
            partial_variables={"format_instructions": self.entry_parser.get_format_instructions()}
        )

    def _create_position_prompt_template(self):
        prompt_str = """
        You are an expert crypto market analyst for a trading bot. You are analyzing an OPEN position for {trading_pair}. Your role is to recommend an action and adjust parameters based on new data.

        **Format Instructions:**
        {format_instructions}

        **Open Position Details:**
        - Average Entry Price: {average_price}
        - Current Profit/Loss: {pnl_percentage}%
        - Number of DCA Orders: {dca_orders}

        **New Market Data:**
        - Current Price: {current_price}
        - Recent News: {news_headlines}
        - Social Media Sentiment Score: {sentiment_score}
        - Technical Indicator (RSI 4h): {rsi}

        Based on the new data, should the bot hold the position, or sell now? Provide your reasoning and suggest adjusted parameters if holding. For example, if the market becomes more bullish, you might increase the take-profit target. If volatility increases, you might tighten the trailing stop.
        """
        return ChatPromptTemplate.from_template(
            template=prompt_str,
            partial_variables={"format_instructions": self.position_parser.get_format_instructions()}
        )

    def analyze_for_new_entry(self, rsi: float):
        """Queries the LLM for analysis on whether to enter a new trade."""
        if not self.config['ai_enabled']:
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}

        logger.info("Querying AI for new entry analysis...")
        # In a real implementation, fetch real data and call the LLM
        fear_and_greed_index = fg.get_fear_and_greed_index()

        market_data = {
            "trading_pair": self.config['trading_pair'],
            "news_headlines": self.news_fetcher.get_news('Bitcoin, Ethereum, Crypto'),
            "sentiment_score": f" value: {fear_and_greed_index[0]}, value_classification: { fear_and_greed_index[1]}",
            "rsi": rsi
        }

        chain = self.entry_prompt | self.llm | self.entry_parser
        try:
            result = chain.invoke(market_data)
            favorable_sentiments = ["Bullish", "Very Bullish"]
            result["is_entry_favorable"] = result["market_sentiment"] in favorable_sentiments and result["confidence_score"] > 0.6
            logger.info(f"AI Entry Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for entry analysis: {e}")
            return {"market_sentiment": "Neutral", "is_entry_favorable": False}
        

    def analyze_open_position(self, position, current_price, rsi: float ):
        """Queries the LLM for analysis on an existing trade."""
        if not self.config['ai_enabled']:
            return {"action": "hold", "reasoning": "AI disabled."}
        
        current_price = float(current_price)
        logger.info("Querying AI for open position analysis...")

        pnl_percentage = ((current_price - position['average_price']) / position['average_price']) * 100
        
        fear_and_greed_index = fg.get_fear_and_greed_index()
        market_data = {
            "trading_pair": self.config['trading_pair'],
            "average_price": position['average_price'],
            "pnl_percentage": f"{pnl_percentage:.2f}",
            "dca_orders": len(position['dca_orders']),
            "current_price": current_price,
            "news_headlines": self.news_fetcher.get_news('Bitcoin, Ethereum, Crypto'),
            "sentiment_score":  f" value: {fear_and_greed_index[0]}, value_classification: { fear_and_greed_index[1]}",
            "rsi": rsi
        }

        chain = self.position_prompt | self.llm | self.position_parser
        try:
            result = chain.invoke(market_data)
            logger.info(f"AI Position Analysis received: {result}")
            return result
        except Exception as e:
            logger.error(f"Error calling AI for position analysis: {e}")
            return {"action": "hold", "reasoning": "AI call failed."}
        