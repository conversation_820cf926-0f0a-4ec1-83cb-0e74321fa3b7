import yaml
import time
from binance.enums import SIDE_BUY, SIDE_SELL
from utils.logger_setup import setup_logger
from utils.state_manager import StateManager
from services.ai_engine import AIEngine
from services.binance_client import BinanceClient
from utils.google_news import GoogleNews
from services.position_validator import PositionValidator

class TradingBot:
    def __init__(self, config_path="config.yaml"):
        self.config = self._load_config(config_path)
        self.logger = setup_logger(self.config['log_file'])
        self.state_manager = StateManager(self.config['state_file'])
        self.google_news = GoogleNews()
        self.ai_engine = AIEngine(self.config, self.google_news)
        self.binance_client = BinanceClient(self.config)
        self.position_validator = PositionValidator(self.binance_client, self.state_manager, self.config)
        self.last_entry_ai_check = 0

    def _load_config(self, path):
        """Loads the YAML configuration file."""
        try:
            with open(path, 'r') as f:
                file_content = f.read()
                #yaml_content = file_content.split('"""')[1]
                return yaml.safe_load(file_content)
            
        except Exception as e:
            raise Exception(f"Failed to load config from {path}: {e}")

    def run(self):
        """The main operational loop of the bot."""
        self.logger.info("Starting DCA Trading Bot...")
        while True:
            try:
                self.main_loop()
            except Exception as e:
                self.logger.error(f"An unexpected error occurred in the main loop: {e}", exc_info=True)
            
            time.sleep(self.config['check_interval_seconds'])

    def main_loop(self):
        """A single cycle of the bot's logic."""
        current_price = self.binance_client.get_market_price()
        position = self.state_manager.get_position()

        if position:
            # Validate position integrity before managing it
            if self.position_validator.validate_position_integrity(position):
                self.manage_existing_position(position, current_price)
            else:
                self.logger.warning("Position validation failed. Attempting recovery...")
                self.position_validator.log_position_status(position)

                # Try to sync with Binance
                if self.position_validator.sync_position_with_binance(position):
                    self.logger.info("Position recovered through sync. Continuing management.")
                    self.manage_existing_position(position, current_price)
                else:
                    # Try to recover from order history
                    recovered_position = self.position_validator.recover_position_from_orders()
                    if recovered_position:
                        self.logger.info("Position recovered from order history.")
                        self.state_manager.set_position(recovered_position)
                        self.manage_existing_position(recovered_position, current_price)
                    else:
                        self.logger.error("Could not recover position. Clearing invalid position.")
                        self.state_manager.clear_position(position)
        else:
            self.look_for_new_entry(current_price)

    def manage_existing_position(self, position, current_price):
        """Handles logic for an already open position, now with AI intervention."""
        current_price = float(current_price)
        self.logger.info(f"Managing position. Avg Price: {position['average_price']:.2f}, Curr Price: {current_price:.2f}, TP: {position['take_profit_price']:.2f}")

        # Periodic position validation (every 30 minutes)
        last_validation = position.get('last_validation_ts', 0)
        if time.time() - last_validation > 1800:  # 30 minutes
            self.logger.info("Performing periodic position validation...")
            if not self.position_validator.validate_position_integrity(position):
                self.logger.error("Position validation failed during management. Attempting sync.")
                self.position_validator.log_position_status(position)
                if not self.position_validator.sync_position_with_binance(position):
                    self.logger.error("Position sync failed. Clearing invalid position.")
                    self.state_manager.clear_position(position)
                    return
            position['last_validation_ts'] = time.time()
            self.state_manager.set_position(position)

        # Periodically ask AI for advice on the open position
        if time.time() - position.get('last_ai_check_ts', 0) > self.config['ai_analysis_interval_minutes'] * 60:
            self.logger.info("Querying AI for analysis on open position.")
            position['last_ai_check_ts'] = time.time()
            analysis = self.ai_engine.analyze_open_position(position, current_price, self.binance_client.get_rsi())

            if analysis.get('action') == 'sell_now':
                self.logger.warning(f"AI recommends selling position! Reason: {analysis.get('reasoning')}")
                success = self.close_position(position, current_price, "AI Sell Signal")
                if success:
                    return
                else:
                    self.logger.error("Failed to execute AI-recommended sell order. Position remains open.")

            # Dynamically adjust parameters based on AI feedback
            self._adjust_params_from_ai(position, analysis)

        # 1. Update and check trailing stop
        self._update_trailing_stop(position, current_price)
        if self._is_trailing_stop_triggered(position, current_price):
            self.logger.info(f"Trailing stop triggered at {position['trailing_stop_price']:.2f}. Selling position.")
            success = self.close_position(position, current_price, "Trailing Stop")
            if success:
                return
            else:
                self.logger.error("Failed to execute trailing stop sell order. Position remains open.")

        # 2. Check for take profit
        if current_price >= position['take_profit_price']:
            self.logger.info(f"Take profit target of {position['take_profit_price']:.2f} reached. Selling.")
            success = self.close_position(position, current_price, "Take Profit")
            if success:
                return
            else:
                self.logger.error("Failed to execute take profit sell order. Position remains open.")

        # 3. Check for new DCA order
        next_dca_price = position['average_price'] * (1 - self.config['dca_deviation_percentage'] / 100)
        if current_price <= next_dca_price and len(position['dca_orders']) < self.config['max_dca_orders']:
            self.logger.info(f"Price dropped to DCA level ({next_dca_price:.2f}). Placing new buy order.")
            success = self.execute_dca_order(position, current_price)
            if not success:
                self.logger.warning("Failed to execute DCA order despite price trigger.")
        
        # Save any changes made (like trailing stop updates or AI param changes)
        self.state_manager.set_position(position)

    def _adjust_params_from_ai(self, position, analysis):
        """Updates position parameters based on AI analysis."""
        if analysis.get('new_take_profit_percentage'):
            new_tp_perc = analysis['new_take_profit_percentage']
            position['take_profit_percentage'] = new_tp_perc
            position['take_profit_price'] = position['average_price'] * (1 + new_tp_perc / 100)
            self.logger.info(f"AI adjusted Take Profit to {new_tp_perc}%. New TP Price: {position['take_profit_price']:.2f}")

        if analysis.get('new_trailing_stop_deviation_percentage'):
            new_ts_dev = analysis['new_trailing_stop_deviation_percentage']
            position['trailing_stop_deviation_percentage'] = new_ts_dev
            self.logger.info(f"AI adjusted Trailing Stop Deviation to {new_ts_dev}%.")

    def look_for_new_entry(self, current_price):
        """Handles logic for entering a new position."""
        self.logger.info("No open position. Looking for a new entry opportunity.")
        
        if time.time() - self.last_entry_ai_check > self.config['ai_analysis_interval_minutes'] * 60:
            analysis = self.ai_engine.analyze_for_new_entry(self.binance_client.get_rsi())
            self.last_entry_ai_check = time.time()

            if analysis.get("is_entry_favorable"):
                self.logger.info("AI analysis is favorable. Entering new position.")
                initial_order_size = self.config['initial_order_size_quote']
                if analysis.get('confidence_score', 0) > 0.8:
                    initial_order_size *= 1.1
                    self.logger.info(f"High AI confidence. Adjusted initial order size to: {initial_order_size:.2f}")

                success = self.open_new_position(initial_order_size, current_price)
                if not success:
                    self.logger.warning("Failed to open new position despite favorable AI analysis.")
            else:
                self.logger.info("AI analysis is not favorable for entry. Waiting.")
        else:
            self.logger.debug("Not time for AI entry check yet. Waiting.")

    def open_new_position(self, order_size_quote, price):
        """Executes the initial buy order and creates a new position state."""
        price = float(price)
        self.logger.info(f"Attempting to open new position with {order_size_quote:.2f} USDT at price {price:.2f}")

        # Execute the buy order using quote amount for better precision
        order_result = self.binance_client.execute_market_order_quote(
            SIDE_BUY, order_size_quote
        )

        if order_result is None:
            self.logger.error("Failed to execute initial buy order. Position not opened.")
            return False

        # Extract actual execution details from order result
        executed_qty = float(order_result.get('executedQty', 0))
        cumulative_quote_qty = float(order_result.get('cummulativeQuoteQty', order_size_quote))

        # Debug logging to see what Binance actually returned
        self.logger.info(f"Order execution details:")
        self.logger.info(f"  Order ID: {order_result.get('orderId')}")
        self.logger.info(f"  Status: {order_result.get('status')}")
        self.logger.info(f"  Executed Qty: {executed_qty}")
        self.logger.info(f"  Cumulative Quote Qty: {cumulative_quote_qty}")
        self.logger.info(f"  Original Qty: {order_result.get('origQty', 'N/A')}")

        # Validate the execution makes sense
        if executed_qty <= 0:
            self.logger.error(f"Invalid executed quantity: {executed_qty}")
            return False

        if cumulative_quote_qty <= 0:
            self.logger.error(f"Invalid cumulative quote quantity: {cumulative_quote_qty}")
            return False

        actual_avg_price = cumulative_quote_qty / executed_qty

        # Sanity check: price should be reasonable
        current_market_price = float(self.binance_client.get_market_price())
        if abs(actual_avg_price - current_market_price) > current_market_price * 0.1:  # 10% tolerance
            self.logger.warning(f"Execution price {actual_avg_price:.2f} differs significantly from market price {current_market_price:.2f}")

        # Sanity check: quantity should match expected order size
        expected_qty = order_size_quote / current_market_price
        if abs(executed_qty - expected_qty) > expected_qty * 0.1:  # 10% tolerance
            self.logger.warning(f"Executed quantity {executed_qty:.8f} differs from expected {expected_qty:.8f}")

        position_data = {
            "entry_price": actual_avg_price,
            "average_price": actual_avg_price,
            "total_base_quantity": executed_qty,
            "total_quote_spent": cumulative_quote_qty,
            "take_profit_percentage": self.config['take_profit_percentage'],
            "trailing_stop_deviation_percentage": self.config['trailing_stop_deviation_percentage'],
            "take_profit_price": actual_avg_price * (1 + self.config['take_profit_percentage'] / 100),
            "dca_orders": [],
            "trailing_stop_activated": False,
            "trailing_stop_peak_price": 0,
            "trailing_stop_price": 0,
            "status": "open",
            "last_ai_check_ts": time.time(),
            "order_id": order_result.get('orderId')
        }

        self.state_manager.set_position(position_data)
        self.logger.info(f"New position opened successfully. Average price: {actual_avg_price:.2f}, Quantity: {executed_qty:.8f}")
        return True

    def execute_dca_order(self, position, price):
        """Executes a subsequent DCA (safety) order and updates the position."""
        last_order_size = position['dca_orders'][-1]['quote_size'] if position['dca_orders'] else self.config['initial_order_size_quote']
        dca_order_size_quote = last_order_size * self.config['dca_order_size_multiplier']

        self.logger.info(f"Attempting DCA order of {dca_order_size_quote:.2f} USDT at price {price:.2f}")

        # Execute the DCA buy order
        order_result = self.binance_client.execute_market_order_quote(
            SIDE_BUY, dca_order_size_quote
        )

        if order_result is None:
            self.logger.error("Failed to execute DCA order. Position not updated.")
            return False

        # Extract actual execution details
        executed_qty = float(order_result.get('executedQty', 0))
        cumulative_quote_qty = float(order_result.get('cummulativeQuoteQty', dca_order_size_quote))
        actual_avg_price = cumulative_quote_qty / executed_qty if executed_qty > 0 else price

        # Update position with actual executed values
        position['dca_orders'].append({
            "price": actual_avg_price,
            "quote_size": cumulative_quote_qty,
            "base_quantity": executed_qty,
            "order_id": order_result.get('orderId')
        })
        position['total_quote_spent'] += cumulative_quote_qty
        position['total_base_quantity'] += executed_qty
        position['average_price'] = position['total_quote_spent'] / position['total_base_quantity']
        position['take_profit_price'] = position['average_price'] * (1 + position['take_profit_percentage'] / 100)

        self.state_manager.set_position(position)
        self.logger.info(f"DCA order filled successfully. New avg price: {position['average_price']:.2f}, New TP: {position['take_profit_price']:.2f}")
        return True

    def close_position(self, position, price, reason):
        """Executes a market sell for the entire position and records the trade."""
        self.logger.info(f"Attempting to close position of {position['total_base_quantity']:.8f} for reason: {reason}")

        # Execute the sell order
        order_result = self.binance_client.execute_market_order(
            SIDE_SELL, position['total_base_quantity']
        )

        if order_result is None:
            self.logger.error("Failed to execute sell order. Position remains open.")
            return False

        # Extract actual execution details
        executed_qty = float(order_result.get('executedQty', 0))
        cumulative_quote_qty = float(order_result.get('cummulativeQuoteQty', 0))
        actual_exit_price = cumulative_quote_qty / executed_qty if executed_qty > 0 else price

        # Calculate actual profit based on executed amounts
        profit = cumulative_quote_qty - position['total_quote_spent']
        profit_percentage = (profit / position['total_quote_spent']) * 100

        self.logger.info(f"Position closed successfully. Exit price: {actual_exit_price:.2f}, Profit: {profit:.2f} USDT ({profit_percentage:.2f}%)")

        closed_trade_data = {
            **position,
            "exit_price": actual_exit_price,
            "profit": profit,
            "profit_percentage": profit_percentage,
            "close_reason": reason,
            "status": "closed",
            "close_order_id": order_result.get('orderId'),
            "actual_sold_quantity": executed_qty,
            "actual_received_quote": cumulative_quote_qty
        }
        self.state_manager.clear_position(closed_trade_data)
        return True

    def _update_trailing_stop(self, position, current_price):
        """Updates the trailing stop price based on the current price."""
        activation_price = position['average_price'] * (1 + self.config['trailing_stop_activation_percentage'] / 100)
        
        if not position['trailing_stop_activated'] and current_price >= activation_price:
            position['trailing_stop_activated'] = True
            position['trailing_stop_peak_price'] = current_price
            self.logger.info(f"Trailing stop activated at price {current_price:.2f}")

        if position['trailing_stop_activated']:
            if current_price > position['trailing_stop_peak_price']:
                position['trailing_stop_peak_price'] = current_price
            
            trailing_dev_perc = position['trailing_stop_deviation_percentage']
            position['trailing_stop_price'] = position['trailing_stop_peak_price'] * (1 - trailing_dev_perc / 100)
            self.logger.debug(f"Trailing stop updated. Peak: {position['trailing_stop_peak_price']:.2f}, Stop: {position['trailing_stop_price']:.2f}")
    
    def _is_trailing_stop_triggered(self, position, current_price):
        """Checks if the current price has triggered the trailing stop."""
        return position['trailing_stop_activated'] and current_price <= position['trailing_stop_price']





